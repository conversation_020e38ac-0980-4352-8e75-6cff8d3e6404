import { useNavigate } from "react-router-dom";
import { useAuth } from "../contexts/AuthContext";
import { useTheme } from "../contexts/ThemeContext";
import Tooltip from "../components/Tooltip";
import { useState, useEffect } from "react";
import { getFirestore, doc, getDoc } from "firebase/firestore";
import { Mosque } from "../models/Mosque";
import { MOSQUES_COLLECTION } from "../constants/firebase";

export default function DashboardPage() {
  const navigate = useNavigate();
  const { logout, userProfile } = useAuth();
  const { isDarkMode, toggleDarkMode } = useTheme();
  const [mosqueName, setMosqueName] = useState<string>("");

  useEffect(() => {
    const fetchMosqueData = async () => {
      if (userProfile?.mosqueId) {
        const db = getFirestore();
        const mosqueDoc = await getDoc(doc(db, MOSQUES_COLLECTION, userProfile.mosqueId));
        if (mosqueDoc.exists()) {
          const mosqueData = mosqueDoc.data() as Mosque;
          setMosqueName(mosqueData.name);
        }
      }
    };

    fetchMosqueData();
  }, [userProfile]);

  const handleLogout = async () => {
    try {
      await logout();
      navigate("/");
    } catch (error) {
      console.error("Failed to log out:", error);
    }
  };

  const handleEditMosque = () => {
    navigate("/edit-mosque");
  };

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900 transition-colors duration-200 overflow-x-hidden">
      {/* Top Bar */}
      <div className="bg-green-600 dark:bg-green-700 shadow-lg transition-colors duration-200">
        <div className="container mx-auto px-4 py-3">
          <div className="flex justify-between items-center">
            {/* Left side - Theme toggle */}
            <div className="flex items-center">
              <Tooltip text={isDarkMode ? "وضع النهار" : "وضع الليل"}>
                <button
                  onClick={toggleDarkMode}
                  className="p-2 rounded-lg bg-green-500 dark:bg-green-600 hover:bg-green-400 dark:hover:bg-green-500 transition-colors"
                  aria-label="Toggle dark mode"
                >
                  {isDarkMode ? (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 sm:h-6 sm:w-6 text-yellow-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"
                      />
                    </svg>
                  ) : (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 sm:h-6 sm:w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"
                      />
                    </svg>
                  )}
                </button>
              </Tooltip>
            </div>

            {/* Right side - Logout button */}
            <div className="flex items-center">
              <Tooltip text="خروج">
                <button onClick={handleLogout} className="p-2 rounded-lg bg-red-500 dark:bg-red-600 hover:bg-red-400 dark:hover:bg-red-500 transition-colors" aria-label="Logout">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 sm:h-6 sm:w-6 text-white" viewBox="0 0 512 512" fill="currentColor">
                    <path d="M192 277.4h189.7l-43.6 44.7L368 352l96-96-96-96-31 29.9 44.7 44.7H192v42.8z" />
                    <path d="M255.7 421.3c-44.1 0-85.5-17.2-116.7-48.4-31.2-31.2-48.3-72.7-48.3-116.9 0-44.1 17.2-85.7 48.3-116.9 31.2-31.2 72.6-48.4 116.7-48.4 44 0 85.3 17.1 116.5 48.2l30.3-30.3c-8.5-8.4-17.8-16.2-27.7-23.2C339.7 61 298.6 48 255.7 48 141.2 48 48 141.3 48 256s93.2 208 207.7 208c42.9 0 84-13 119-37.5 10-7 19.2-14.7 27.7-23.2l-30.2-30.2c-31.1 31.1-72.5 48.2-116.5 48.2zM448.004 256.847l-.849-.848.849-.849.848.849z" />
                  </svg>
                </button>
              </Tooltip>
            </div>
          </div>
        </div>
      </div>

      {/* Mosque Name and User Section */}
      <div className="bg-gray-100 dark:bg-gray-800 shadow-md mt-4">
        <div className="container mx-auto px-4 py-3 text-center">
          <h1 className="text-2xl font-bold text-gray-800 dark:text-white">{mosqueName || "المسجد"}</h1>
          <p className="text-gray-600 dark:text-gray-300 mt-2">{userProfile?.name || "مرحباً"}</p>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        <div className="grid gap-4 max-w-md mx-auto sm:max-w-lg md:max-w-md lg:max-w-sm">
          <button onClick={handleEditMosque} className="bg-green-500 hover:bg-green-600 text-white p-4 rounded-lg transition-colors text-right">
            تعديل المسجد
          </button>
          <button onClick={() => navigate("/profile")} className="bg-green-500 hover:bg-green-600 text-white p-4 rounded-lg transition-colors text-right">
            تعديل الملف الشخصي
          </button>
          {/* {userProfile?.isSysAdmin && (
            <button
              onClick={() => navigate('/system-config')}
              className="bg-green-500 hover:bg-green-600 text-white p-4 rounded-lg transition-colors text-right"
            >
              إعدادات النظام
            </button>
          )} */}
        </div>
      </div>
    </div>
  );
}
