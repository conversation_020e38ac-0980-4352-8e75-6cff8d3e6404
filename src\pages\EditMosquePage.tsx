import { useState, FormEvent, useEffect } from "react";
import { Mosque, SliderItem } from "../models/Mosque";
import { MediaItem } from "../models/MediaItem";
import { useAuth } from "../contexts/AuthContext";
import { doc, getDoc, getFirestore, collection, getDocs, updateDoc, setDoc, deleteDoc } from "firebase/firestore";
import { useNavigate } from "react-router-dom";
import { useTheme } from "../contexts/ThemeContext";
import Tooltip from "../components/Tooltip";
import ConfirmDialog from "../components/ConfirmDialog";
import { ref, uploadBytesResumable, getDownloadURL, getStorage, deleteObject } from "firebase/storage";
import { MOSQUES_COLLECTION } from "../constants/firebase";

// Add Toast type
interface Toast {
  message: string;
  type: "success" | "error";
}

type MosqueFieldValue = string | number | boolean | string[] | SliderItem[];

// interface TabButtonProps {
//   label: string;
//   active?: boolean;
//   onClick?: () => void;
// }

const inputClassName = "w-full p-2 rounded-md border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white";

type TabType = "general" | "slider" | "content" | "settings";

interface ConfirmDialogState {
  isOpen: boolean;
  itemToDelete?: MediaItem;
}

export default function EditMosquePage() {
  const [activeTab, setActiveTab] = useState<TabType>("slider");

  // ... existing code until the form ...

  const TabButton = ({ tab, label }: { tab: TabType; label: string }) => (
    <button
      onClick={() => setActiveTab(tab)}
      className={`px-4 py-2 rounded-t-lg transition-colors ${
        activeTab === tab ? "bg-green-500 text-white" : "bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600"
      }`}
    >
      {label}
    </button>
  );
  const { userProfile, logout } = useAuth();
  const [mosque, setMosque] = useState<Mosque>({
    name: "",
    city: "",
    imamName: "",
    contactPhone: "",
    news: "",
    quoteOpener: "",
    quoteContent: "",
    quoteCloser: "",
    showNews: false,
    showPrayTimes: false,
    sliderDelayTime: 5,
    sliderContentSource: "",
    // Add other required properties from Mosque interface with default values
    id: "",
    mosqueImageUrl: "",
    mosqueTzUtcOffset: 0,
    runningTimeFormat: "",
    prayTimeFormat: "",
    locale: "",
    friendlyUrlName: "",
    backImageUrl: "",
    regionalCity: "",
    dahriShift: 0,
    timeZone: "",
    referenceCity: "",
    dedication: "",
    contactName: "",
    sliderItems: [],
    sliderListId: "",
    dayLightSaving: false,
    sliderMoveTime: "",
    adhanCountDown: 0,
    iqamaFajrShift: 0,
    iqamaAsrShift: 0,
    iqamaShorouqShift: 0,
    iqamaDuhrShift: 0,
    iqamaMaghrebShift: 0,
    iqamaEshaaShift: 0,
    viewPublic: "",
    newsScrollDuration: 40, // Add default value
  });
  const [localSliderItems, setLocalSliderItems] = useState<SliderItem[]>([]);
  // const [activeTab, setActiveTab] = useState("general");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [toast, setToast] = useState<Toast | null>(null);
  const { isDarkMode, toggleDarkMode } = useTheme();
  const navigate = useNavigate();
  const [deleteDialogState, setDeleteDialogState] = useState({
    isOpen: false,
    slideIndex: -1,
  });
  const [isMediaDialogOpen, setIsMediaDialogOpen] = useState(false);
  const [mediaItems, setMediaItems] = useState<MediaItem[]>([]);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadTags, setUploadTags] = useState("");
  const [deleteMediaDialogState, setDeleteMediaDialogState] = useState<ConfirmDialogState>({
    isOpen: false,
    itemToDelete: undefined,
  });

  // Add useEffect for toast auto-dismiss
  useEffect(() => {
    if (toast) {
      const timer = setTimeout(() => {
        setToast(null);
      }, 3000); // Toast will disappear after 3 seconds

      return () => clearTimeout(timer);
    }
  }, [toast]);

  // Set initial active tab based on user permissions
  useEffect(() => {
    if (userProfile) {
      setActiveTab(userProfile.isSysAdmin ? "general" : "slider");
    }
  }, [userProfile]);

  useEffect(() => {
    console.log("userProfile", userProfile);
    const loadMosqueData = async () => {
      if (!userProfile?.mosqueId) {
        setError("No mosque ID found");
        setLoading(false);
        return;
      }

      try {
        const db = getFirestore();
        const mosqueRef = doc(db, MOSQUES_COLLECTION, userProfile.mosqueId);
        const mosqueDoc = await getDoc(mosqueRef);

        if (mosqueDoc.exists()) {
          setMosque(mosqueDoc.data() as Mosque);
        } else {
          setError("Mosque not found");
        }
      } catch (err) {
        setError("Error loading mosque data");
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    loadMosqueData();
  }, [userProfile?.mosqueId]);

  useEffect(() => {
    if (mosque?.sliderItems) {
      setLocalSliderItems(mosque.sliderItems);
    }
  }, [mosque?.sliderItems]);

  const handleInputChange = (field: keyof Mosque, value: MosqueFieldValue) => {
    setMosque((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = async (e: FormEvent) => {
    console.log("Submit function called", e ? "with event" : "without event");
    if (e) {
      e.preventDefault();
    }

    setLoading(true);
    setError("");
    setToast(null);

    if (!userProfile?.mosqueId) {
      setError("No mosque ID found");
      setLoading(false);
      return;
    }

    try {
      const db = getFirestore();
      const mosqueRef = doc(db, MOSQUES_COLLECTION, userProfile.mosqueId);

      const mosqueData = {
        ...mosque,
        sliderItems: localSliderItems, // Use the local state for slider items
      };

      await updateDoc(mosqueRef, mosqueData);

      setToast({
        message: "تم حفظ التغييرات بنجاح",
        type: "success",
      });
    } catch (err) {
      setError("Failed to save mosque data");
      setToast({
        message: "فشل في حفظ التغييرات",
        type: "error",
      });
      console.error("Error saving mosque:", err);
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    navigate("/dashboard");
  };

  const handleLogout = async () => {
    try {
      await logout();
      navigate("/");
    } catch (error) {
      console.error("Failed to log out:", error);
    }
  };

  const handleDeleteSlide = (index: number) => {
    setDeleteDialogState({
      isOpen: true,
      slideIndex: index,
    });
  };

  const confirmDeleteSlide = () => {
    const index = deleteDialogState.slideIndex;
    if (index !== -1) {
      const newSlides = [...localSliderItems];
      newSlides.splice(index, 1);
      setLocalSliderItems(newSlides);

      // Remove this toast notification
      // setToast({
      //   message: "تم حذف الشريحة بنجاح",
      //   type: "success",
      // });
    }
    setDeleteDialogState({ isOpen: false, slideIndex: -1 });
  };

  const loadMediaItems = async () => {
    try {
      const db = getFirestore();
      const mediaCollection = collection(db, "media");
      const mediaSnapshot = await getDocs(mediaCollection);
      const mediaList = mediaSnapshot.docs.map((doc) => doc.data() as MediaItem);

      // Sort by createdAt in descending order
      const sortedMediaList = mediaList.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

      setMediaItems(sortedMediaList);
    } catch (error) {
      console.error("Error loading media items:", error);
      // Don't set toast here, as it might be causing issues
    }
  };

  const handleOpenMediaDialog = (e: React.MouseEvent) => {
    // Prevent the event from bubbling up to the form
    e.preventDefault();
    e.stopPropagation();

    console.log("Opening media dialog");
    setToast(null);
    loadMediaItems();
    setIsMediaDialogOpen(true);
  };

  const handleMediaSelect = (mediaItem: MediaItem) => {
    const newSlide: SliderItem = {
      imageUrl: mediaItem.url,
      text: mediaItem.alt || "",
    };

    const newSlides = [...localSliderItems, newSlide];
    setLocalSliderItems(newSlides);

    setIsMediaDialogOpen(false);
    // Remove this toast notification
    // setToast({
    //   message: "تمت إضافة الشريحة بنجاح",
    //   type: "success",
    // });
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      setSelectedFile(event.target.files[0]);
    }
  };

  const handleUpload = async () => {
    if (!selectedFile) return;

    setIsUploading(true);
    setUploadProgress(0);

    try {
      const now = new Date();
      const createdAt = now.toISOString();
      const yearMonth = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, "0")}`;

      // Clean filename and create ID
      const cleanFileName = selectedFile.name.replace(/[^a-zA-Z0-9.-]/g, "-");
      const id = `${createdAt.replace(/:/g, "-")}_${cleanFileName}`;

      // Create full path
      const fullPath = `masajed/${yearMonth}/${cleanFileName}`;

      // Upload to Firebase Storage
      const storage = getStorage();
      const storageRef = ref(storage, fullPath);
      const uploadTask = uploadBytesResumable(storageRef, selectedFile);

      uploadTask.on(
        "state_changed",
        (snapshot) => {
          const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
          setUploadProgress(progress);
        },
        (error) => {
          console.error("Upload error:", error);
          setToast({
            message: "فشل في رفع الصورة",
            type: "error",
          });
          setIsUploading(false);
        },
        async () => {
          // Get download URL
          const url = await getDownloadURL(uploadTask.snapshot.ref);

          // Create media item
          const mediaItem: MediaItem = {
            createdAt,
            fileName: cleanFileName,
            id,
            fullPath,
            alt: cleanFileName,
            type: selectedFile.type,
            url,
            tags: uploadTags,
            createdBy: userProfile?.id || "",
          };

          // Add to Firestore with custom ID
          const db = getFirestore();
          await setDoc(doc(db, "media", id), mediaItem);

          // Update UI
          setMediaItems((prev) => [mediaItem, ...prev]);
          setSelectedFile(null);
          setUploadProgress(0);
          setUploadTags(""); // Reset tags
          setIsUploading(false);
          setToast({
            message: "تم رفع الصورة بنجاح",
            type: "success",
          });
        }
      );
    } catch (error) {
      console.error("Upload error:", error);
      setToast({
        message: "فشل في رفع الصورة",
        type: "error",
      });
      setIsUploading(false);
    }
  };

  const handleDeleteMedia = async (item: MediaItem) => {
    try {
      const storage = getStorage();
      const fileRef = ref(storage, item.fullPath);
      const db = getFirestore();

      // Delete from Storage
      await deleteObject(fileRef);

      // Delete from Firestore
      await deleteDoc(doc(db, "media", item.id));

      // Update UI
      setMediaItems((prev) => prev.filter((mediaItem) => mediaItem.id !== item.id));

      setToast({
        message: "تم حذف الصورة بنجاح",
        type: "success",
      });
    } catch (error) {
      console.error("Delete error:", error);
      setToast({
        message: "فشل في حذف الصورة",
        type: "error",
      });
    }
  };

  useEffect(() => {
    // When selectedFile changes, cleanup the previous objectURL
    return () => {
      if (selectedFile) {
        URL.revokeObjectURL(URL.createObjectURL(selectedFile));
      }
    };
  }, [selectedFile]);

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900">
      {/* Add ConfirmDialog before the Toast Component */}
      <ConfirmDialog
        isOpen={deleteDialogState.isOpen}
        onClose={() => setDeleteDialogState({ isOpen: false, slideIndex: -1 })}
        onConfirm={confirmDeleteSlide}
        title="حذف الشريحة"
        message="هل أنت متأكد من حذف هذه الشريحة؟"
        confirmText="حذف"
        cancelText="إلغاء"
      />

      {/* Toast Component */}
      {toast && (
        <div className="fixed top-28 right-4 z-50 animate-fade-in">
          {" "}
          {/* Changed from top-20 to top-28 */}
          <div className={`rounded-lg px-4 py-3 shadow-lg ${toast.type === "success" ? "bg-green-500 text-white" : "bg-red-500 text-white"}`}>
            <div className="flex items-center">
              {toast.type === "success" ? (
                <svg className="h-5 w-5 mr-2" fill="none" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" viewBox="0 0 24 24" stroke="currentColor">
                  <path d="M5 13l4 4L19 7"></path>
                </svg>
              ) : (
                <svg className="h-5 w-5 mr-2" fill="none" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" viewBox="0 0 24 24" stroke="currentColor">
                  <path d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              )}
              <span>{toast.message}</span>
            </div>
          </div>
        </div>
      )}

      {/* Sticky Header */}
      <div className="fixed top-0 left-0 right-0 z-50">
        {/* Icons Row */}
        <div className="bg-green-600 dark:bg-green-800">
          <div className="container mx-auto px-4 py-2 flex justify-between items-center">
            {/* Right side - Theme toggle */}
            <div className="flex items-center">
              <Tooltip text={isDarkMode ? "وضع النهار" : "وضع الليل"}>
                <button onClick={toggleDarkMode} className="p-2 rounded-lg bg-green-500 dark:bg-green-700 hover:bg-green-400 dark:hover:bg-green-600 transition-colors">
                  {isDarkMode ? (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-yellow-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"
                      />
                    </svg>
                  ) : (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"
                      />
                    </svg>
                  )}
                </button>
              </Tooltip>
            </div>

            {/* Center - Save and View buttons */}
            <div className="flex items-center gap-2">
              {/* Save Button */}
              <Tooltip text="حفظ">
                <button
                  onClick={handleSubmit}
                  disabled={loading}
                  className="p-2 rounded-lg bg-green-500 dark:bg-green-700 hover:bg-green-400 dark:hover:bg-green-600 transition-colors disabled:bg-gray-300 flex justify-center items-center w-10 h-10"
                >
                  {loading ? (
                    <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" />
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      />
                    </svg>
                  ) : (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  )}
                </button>
              </Tooltip>

              {/* View Button */}
              <Tooltip text="عرض">
                <button
                  onClick={() => window.open(`/show/${mosque.friendlyUrlName}`, "_blank")}
                  className="p-2 rounded-lg bg-green-500 dark:bg-green-700 hover:bg-green-400 dark:hover:bg-green-600 transition-colors"
                >
                  <svg className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                    />
                  </svg>
                </button>
              </Tooltip>
            </div>

            {/* Left side - Back and Logout buttons */}
            <div className="flex items-center gap-2">
              <Tooltip text="رجوع">
                <button onClick={handleBack} className="p-2 rounded-lg bg-green-500 dark:bg-green-700 hover:bg-green-400 dark:hover:bg-green-600 transition-colors">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
                    <path
                      fillRule="evenodd"
                      d="M10.293 14.707a1 1 0 010-1.414L13.586 10l-3.293-3.293a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                </button>
              </Tooltip>
              <Tooltip text="خروج">
                <button onClick={handleLogout} className="p-2 rounded-lg bg-red-500 hover:bg-red-400 dark:bg-red-600 dark:hover:bg-red-500 transition-colors">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" viewBox="0 0 512 512" fill="currentColor">
                    <path d="M192 277.4h189.7l-43.6 44.7L368 352l96-96-96-96-31 29.9 44.7 44.7H192v42.8z" />
                    <path d="M255.7 421.3c-44.1 0-85.5-17.2-116.7-48.4-31.2-31.2-48.3-72.7-48.3-116.9 0-44.1 17.2-85.7 48.3-116.9 31.2-31.2 72.6-48.4 116.7-48.4 44 0 85.3 17.1 116.5 48.2l30.3-30.3c-8.5-8.4-17.8-16.2-27.7-23.2C339.7 61 298.6 48 255.7 48 141.2 48 48 141.3 48 256s93.2 208 207.7 208c42.9 0 84-13 119-37.5 10-7 19.2-14.7 27.7-23.2l-30.2-30.2c-31.1 31.1-72.5 48.2-116.5 48.2zM448.004 256.847l-.849-.848.849-.849.848.849z" />
                  </svg>
                </button>
              </Tooltip>
            </div>
          </div>
        </div>
        {/* Mosque Info Row */}
        <div className="bg-gray-50 dark:bg-gray-700 shadow-sm">
          <div className="container mx-auto px-4 py-2 text-center">
            <div className="text-xl md:text-2xl font-bold text-gray-800 dark:text-white">
              {mosque.name || "المسجد"} - {mosque.city || "المدينة"}
            </div>
          </div>
        </div>
      </div>

      {/* Add padding to account for fixed header */}
      <div className="pt-28 overflow-y-auto min-h-screen">
        {loading ? (
          <div className="relative max-w-2xl mx-auto">
            <div className="absolute inset-0 bg-white/80 dark:bg-gray-800/80 flex justify-center items-center z-10">
              <div className="text-xl text-gray-600 dark:text-gray-300 flex items-center">
                <svg className="animate-spin h-5 w-5 mr-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                جاري التحميل...
              </div>
            </div>
            <div className="opacity-50 pointer-events-none">{/* Your form content */}</div>
          </div>
        ) : error ? (
          <div className="flex justify-center items-center h-screen">
            <div className="text-xl text-red-600 dark:text-red-400">{error}</div>
          </div>
        ) : (
          <div className="max-w-2xl mx-auto">
            <div className="p-4 md:p-6">
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="flex gap-2 mb-6 border-b border-gray-200 dark:border-gray-700">
                  {userProfile?.isSysAdmin && <TabButton tab="general" label="عام" />}
                  <TabButton tab="slider" label="شرائح" />
                  <TabButton tab="content" label="شريط" />
                  <TabButton tab="settings" label="إعدادات" />
                </div>
                {activeTab === "general" && (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">اسم المسجد</label>
                      <input type="text" value={mosque.name} onChange={(e) => handleInputChange("name", e.target.value)} className={inputClassName} />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">المدينة</label>
                      <input type="text" value={mosque.city} onChange={(e) => handleInputChange("city", e.target.value)} className={inputClassName} />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">المدينة الإقليمية</label>
                      <input type="text" value={mosque.regionalCity} onChange={(e) => handleInputChange("regionalCity", e.target.value)} className={inputClassName} />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">المدينة المرجعية</label>
                      <input type="text" value={mosque.referenceCity} onChange={(e) => handleInputChange("referenceCity", e.target.value)} className={inputClassName} />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">اسم الإمام</label>
                      <input type="text" value={mosque.imamName} onChange={(e) => handleInputChange("imamName", e.target.value)} className={inputClassName} />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">اسم المسؤول</label>
                      <input type="text" value={mosque.contactName} onChange={(e) => handleInputChange("contactName", e.target.value)} className={inputClassName} />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">رقم الهاتف</label>
                      <input type="text" value={mosque.contactPhone} onChange={(e) => handleInputChange("contactPhone", e.target.value)} className={inputClassName} />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">المنطقة الزمنية</label>
                      <input type="text" value={mosque.timeZone} onChange={(e) => handleInputChange("timeZone", e.target.value)} className={inputClassName} />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">فرق التوقيت UTC</label>
                      <input
                        type="number"
                        value={mosque.mosqueTzUtcOffset}
                        onChange={(e) => handleInputChange("mosqueTzUtcOffset", parseInt(e.target.value))}
                        className={inputClassName}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">اسم الرابط المختصر</label>
                      <input type="text" value={mosque.friendlyUrlName} onChange={(e) => handleInputChange("friendlyUrlName", e.target.value)} className={inputClassName} />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">اللغة</label>
                      <input type="text" value={mosque.locale} onChange={(e) => handleInputChange("locale", e.target.value)} className={inputClassName} />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">
                        فرق التوقيت الدهري <span className="text-green-600 dark:text-green-400">(دقائق)</span>
                      </label>
                      <input type="number" value={mosque.dahriShift} onChange={(e) => handleInputChange("dahriShift", parseInt(e.target.value))} className={inputClassName} />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">الإهداء</label>
                      <input type="text" value={mosque.dedication} onChange={(e) => handleInputChange("dedication", e.target.value)} className={inputClassName} />
                    </div>
                  </div>
                )}

                {activeTab === "slider" && (
                  <div className="space-y-6">
                    {/* Slides List */}
                    <div className="mt-6">
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                          الشرائح <span className="text-green-600 dark:text-green-400">({localSliderItems.length})</span>
                        </h3>
                        <button
                          type="button"
                          onClick={handleOpenMediaDialog}
                          className="p-2 rounded-lg bg-green-500 hover:bg-green-400 dark:bg-green-700 dark:hover:bg-green-600 text-white transition-colors"
                        >
                          <svg className="w-5 h-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 3a1 1 0 00-1 1v5H4a1 1 0 100 2h5v5a1 1 0 102 0v-5h5a1 1 0 100-2h-5V4a1 1 0 00-1-1z" clipRule="evenodd" />
                          </svg>
                        </button>
                      </div>
                      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {localSliderItems.map((slide, index) => (
                          <div key={index} className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden w-full">
                            {/* Control Bar */}
                            <div className="bg-gray-50 dark:bg-gray-700 px-3 py-2 flex items-center justify-between border-b border-gray-200 dark:border-gray-600">
                              {/* Slide Number (Right) */}
                              <span className="text-base font-medium text-gray-700 dark:text-gray-200">{index + 1}</span>

                              {/* Navigation Buttons (Center) */}
                              <div className="flex items-center gap-2">
                                <Tooltip text="تحريك للأمام">
                                  <button
                                    type="button"
                                    onClick={() => {
                                      if (index > 0) {
                                        const newSlides = [...localSliderItems];
                                        [newSlides[index], newSlides[index - 1]] = [newSlides[index - 1], newSlides[index]];
                                        setLocalSliderItems(newSlides);
                                      }
                                    }}
                                    disabled={index === 0}
                                    className={`p-1.5 rounded-lg transition-colors ${
                                      index === 0
                                        ? "text-gray-400 dark:text-gray-600 cursor-not-allowed"
                                        : "text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600"
                                    }`}
                                  >
                                    <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                    </svg>
                                  </button>
                                </Tooltip>

                                <Tooltip text="تحريك للخلف">
                                  <button
                                    type="button"
                                    onClick={() => {
                                      if (index < localSliderItems.length - 1) {
                                        const newSlides = [...localSliderItems];
                                        [newSlides[index], newSlides[index + 1]] = [newSlides[index + 1], newSlides[index]];
                                        setLocalSliderItems(newSlides);
                                      }
                                    }}
                                    disabled={index === localSliderItems.length - 1}
                                    className={`p-1.5 rounded-lg transition-colors ${
                                      index === localSliderItems.length - 1
                                        ? "text-gray-400 dark:text-gray-600 cursor-not-allowed"
                                        : "text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600"
                                    }`}
                                  >
                                    <svg className="h-5 w-5 transform rotate-180" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                    </svg>
                                  </button>
                                </Tooltip>
                              </div>

                              {/* Delete Button (Left) */}
                              <Tooltip text="حذف">
                                <button
                                  type="button"
                                  onClick={() => handleDeleteSlide(index)}
                                  className="p-1.5 text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors"
                                >
                                  <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path
                                      fillRule="evenodd"
                                      d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9z"
                                      clipRule="evenodd"
                                    />
                                  </svg>
                                </button>
                              </Tooltip>
                            </div>

                            {/* Slide Image Container with Fixed Dimensions */}
                            <div className="w-full aspect-video">
                              <img
                                src={slide.imageUrl}
                                alt={`Slide ${index + 1}`}
                                className="w-full h-full object-cover"
                                onError={(e) => {
                                  const target = e.target as HTMLImageElement;
                                  target.src = "placeholder-image-url.jpg";
                                }}
                              />
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                {activeTab === "content" && (
                  <div className="space-y-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">محتوى الشريط</label>
                      <textarea
                        value={mosque.news}
                        onChange={(e) => handleInputChange("news", e.target.value)}
                        className={`${inputClassName} min-h-[200px] leading-8 text-xl`}
                        rows={8}
                        style={{ resize: "vertical" }}
                      />
                    </div>
                  </div>
                )}

                {activeTab === "settings" && (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
                    {/* Checkboxes spanning full width */}
                    <div className="col-span-1 md:col-span-2 lg:col-span-3">
                      <div className="flex items-center">
                        <div className="relative">
                          <input type="checkbox" checked={mosque.showNews} onChange={(e) => handleInputChange("showNews", e.target.checked)} className="sr-only" id="showNews" />
                          <label
                            htmlFor="showNews"
                            className={`w-5 h-5 border-2 rounded flex items-center justify-center cursor-pointer transition-colors ${
                              mosque.showNews ? "bg-green-500 border-green-500" : "bg-white border-gray-300 dark:bg-gray-700 dark:border-gray-600"
                            }`}
                          >
                            {mosque.showNews && (
                              <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path
                                  fillRule="evenodd"
                                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                  clipRule="evenodd"
                                />
                              </svg>
                            )}
                          </label>
                        </div>
                        <label htmlFor="showNews" className="mr-3 text-sm font-medium text-gray-700 dark:text-gray-200 cursor-pointer">
                          عرض الشريط
                        </label>
                      </div>
                    </div>

                    <div className="col-span-1 md:col-span-2 lg:col-span-3">
                      <div className="flex items-center">
                        <div className="relative">
                          <input
                            type="checkbox"
                            checked={mosque.showPrayTimes}
                            onChange={(e) => handleInputChange("showPrayTimes", e.target.checked)}
                            className="sr-only"
                            id="showPrayTimes"
                          />
                          <label
                            htmlFor="showPrayTimes"
                            className={`w-5 h-5 border-2 rounded flex items-center justify-center cursor-pointer transition-colors ${
                              mosque.showPrayTimes ? "bg-green-500 border-green-500" : "bg-white border-gray-300 dark:bg-gray-700 dark:border-gray-600"
                            }`}
                          >
                            {mosque.showPrayTimes && (
                              <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path
                                  fillRule="evenodd"
                                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                  clipRule="evenodd"
                                />
                              </svg>
                            )}
                          </label>
                        </div>
                        <label htmlFor="showPrayTimes" className="mr-3 text-sm font-medium text-gray-700 dark:text-gray-200 cursor-pointer">
                          عرض أوقات الصلاة
                        </label>
                      </div>
                    </div>

                    <div className="col-span-1 md:col-span-2 lg:col-span-3">
                      <div className="flex items-center">
                        <div className="relative">
                          <input
                            type="checkbox"
                            checked={mosque.dayLightSaving}
                            onChange={(e) => handleInputChange("dayLightSaving", e.target.checked)}
                            className="sr-only"
                            id="dayLightSaving"
                          />
                          <label
                            htmlFor="dayLightSaving"
                            className={`w-5 h-5 border-2 rounded flex items-center justify-center cursor-pointer transition-colors ${
                              mosque.dayLightSaving ? "bg-green-500 border-green-500" : "bg-white border-gray-300 dark:bg-gray-700 dark:border-gray-600"
                            }`}
                          >
                            {mosque.dayLightSaving && (
                              <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path
                                  fillRule="evenodd"
                                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                  clipRule="evenodd"
                                />
                              </svg>
                            )}
                          </label>
                        </div>
                        <label htmlFor="dayLightSaving" className="mr-3 text-sm font-medium text-gray-700 dark:text-gray-200 cursor-pointer">
                          التوقيت الصيفي
                        </label>
                      </div>
                    </div>

                    {/* Regular input fields */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">
                        مدة عرض الشريحة <span className="text-green-600 dark:text-green-400">(ثواني)</span>
                      </label>
                      <input
                        type="number"
                        value={mosque.sliderDelayTime}
                        onChange={(e) => handleInputChange("sliderDelayTime", parseInt(e.target.value))}
                        className={inputClassName}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">
                        العد التنازلي للأذان <span className="text-green-600 dark:text-green-400">(دقائق)</span>
                      </label>
                      <input
                        type="number"
                        value={mosque.adhanCountDown}
                        onChange={(e) => handleInputChange("adhanCountDown", parseInt(e.target.value))}
                        className={inputClassName}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">
                        تأخير إقامة الفجر <span className="text-green-600 dark:text-green-400">(دقائق)</span>
                      </label>
                      <input
                        type="number"
                        value={mosque.iqamaFajrShift}
                        onChange={(e) => handleInputChange("iqamaFajrShift", parseInt(e.target.value))}
                        className={inputClassName}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">
                        تأخير إقامة الظهر <span className="text-green-600 dark:text-green-400">(دقائق)</span>
                      </label>
                      <input
                        type="number"
                        value={mosque.iqamaDuhrShift}
                        onChange={(e) => handleInputChange("iqamaDuhrShift", parseInt(e.target.value))}
                        className={inputClassName}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">
                        تأخير إقامة العصر <span className="text-green-600 dark:text-green-400">(دقائق)</span>
                      </label>
                      <input type="number" value={mosque.iqamaAsrShift} onChange={(e) => handleInputChange("iqamaAsrShift", parseInt(e.target.value))} className={inputClassName} />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">
                        تأخير إقامة المغرب <span className="text-green-600 dark:text-green-400">(دقائق)</span>
                      </label>
                      <input
                        type="number"
                        value={mosque.iqamaMaghrebShift}
                        onChange={(e) => handleInputChange("iqamaMaghrebShift", parseInt(e.target.value))}
                        className={inputClassName}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">
                        تأخير إقامة العشاء <span className="text-green-600 dark:text-green-400">(دقائق)</span>
                      </label>
                      <input
                        type="number"
                        value={mosque.iqamaEshaaShift}
                        onChange={(e) => handleInputChange("iqamaEshaaShift", parseInt(e.target.value))}
                        className={inputClassName}
                      />
                    </div>
                  </div>
                )}
              </form>
            </div>
          </div>
        )}
      </div>
      {/* Media Selection Dialog */}
      {isMediaDialogOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-4xl w-full max-h-[80vh] overflow-hidden">
            <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
              <h2 className="text-lg font-medium text-gray-900 dark:text-gray-100">اختر صورة</h2>
              <button onClick={() => setIsMediaDialogOpen(false)} className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300">
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* Upload Section */}
            <div className="p-4 border-b border-gray-200 dark:border-gray-700">
              <div className="flex flex-col space-y-4">
                {/* File Input */}
                <div className="flex gap-2">
                  <label className="flex-1 relative">
                    <input type="file" accept="image/*" onChange={handleFileSelect} className="hidden" />
                    <div className="flex-1 p-2 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-700 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-200">
                      {selectedFile ? selectedFile.name : "اختر ملف"}
                    </div>
                  </label>
                  <button
                    onClick={handleUpload}
                    disabled={!selectedFile || isUploading}
                    className={`px-4 py-2 rounded-md ${
                      !selectedFile || isUploading ? "bg-gray-300 cursor-not-allowed" : "bg-green-500 hover:bg-green-600"
                    } text-white transition-colors`}
                  >
                    {isUploading ? "جاري الرفع..." : "رفع"}
                  </button>
                </div>

                {/* Preview and tags section */}
                {selectedFile && (
                  <div className="flex space-x-4 relative">
                    {/* Cancel upload button */}
                    <button
                      onClick={() => {
                        setSelectedFile(null);
                        setUploadTags("");
                        setUploadProgress(0);
                      }}
                      className="absolute -top-2 -right-2 p-1 bg-red-500 hover:bg-red-600 text-white rounded-full shadow-md transition-colors"
                      title="إلغاء"
                    >
                      <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>

                    {/* Image preview */}
                    <div className="flex-shrink-0 ml-4">
                      {" "}
                      {/* Added ml-4 for left margin */}
                      <img src={URL.createObjectURL(selectedFile)} alt="Preview" className="w-40 h-40 object-cover rounded-md" />
                    </div>

                    {/* Tags input */}
                    <div className="flex-grow">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">الوسوم (اختياري)</label>
                      <textarea
                        value={uploadTags}
                        onChange={(e) => setUploadTags(e.target.value)}
                        placeholder="أدخل الوسوم هنا..."
                        className="w-full h-32 p-2 border border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                      />
                    </div>
                  </div>
                )}

                {/* Progress Bar */}
                {isUploading && (
                  <div className="mt-4">
                    <div className="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                      <div className="bg-green-600 h-2.5 rounded-full transition-all duration-300" style={{ width: `${uploadProgress}%` }}></div>
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400 mt-1 text-left">{Math.round(uploadProgress)}%</div>
                  </div>
                )}
              </div>
            </div>

            {/* Image Grid */}
            <div className="p-4 overflow-y-auto max-h-[calc(80vh-16rem)]">
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                {mediaItems.map((item) => (
                  <div
                    key={item.id}
                    className="relative group aspect-[16/9] overflow-hidden rounded-lg border border-gray-200 dark:border-gray-700 hover:border-blue-500 dark:hover:border-blue-400 transition-all duration-300 ease-in-out"
                  >
                    {/* Plus Button - Always visible */}
                    <button
                      onClick={() => handleMediaSelect(item)}
                      className="absolute top-2 right-2 z-10 p-1.5 bg-green-500 hover:bg-green-600 text-white rounded-full shadow-md transition-colors duration-300"
                      title="اختيار"
                    >
                      <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                      </svg>
                    </button>

                    {/* Delete Button - Only visible for system admins on hover */}
                    {userProfile?.isSysAdmin && (
                      <div className="absolute bottom-2 left-2 z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <button
                          onClick={() => {
                            setDeleteMediaDialogState({
                              isOpen: true,
                              itemToDelete: item,
                            });
                          }}
                          className="p-1.5 bg-red-500 hover:bg-red-600 text-white rounded-full shadow-md transition-colors"
                          title="حذف"
                        >
                          <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </button>
                      </div>
                    )}

                    <img src={item.url} alt={item.alt} className="w-full h-full object-cover transition-transform duration-300 ease-in-out group-hover:scale-105" />
                  </div>
                ))}
              </div>
            </div>

            {/* Confirm Delete Dialog */}
            <ConfirmDialog
              isOpen={deleteMediaDialogState.isOpen}
              onClose={() => setDeleteMediaDialogState({ isOpen: false })}
              onConfirm={() => {
                if (deleteMediaDialogState.itemToDelete) {
                  handleDeleteMedia(deleteMediaDialogState.itemToDelete);
                }
                setDeleteMediaDialogState({ isOpen: false });
              }}
              title="حذف الصورة"
              message="هل أنت متأكد من حذف هذه الصورة؟ لا يمكن التراجع عن هذا الإجراء."
              confirmText="حذف"
              cancelText="إلغاء"
            />
          </div>
        </div>
      )}
    </div>
  );
}
