import { createContext, useContext, useEffect, useState } from "react";
import {
  User,
  UserCredential,
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  browserLocalPersistence,
  setPersistence,
} from "firebase/auth";
import { doc, getFirestore, setDoc, collection, query, where, getDocs } from "firebase/firestore";
import { auth } from "../config/firebase";
import { UserProfile } from "../models/User";

interface AuthContextType {
  currentUser: User | null;
  userProfile: UserProfile | null;
  signUp: (email: string, password: string, phone: string) => Promise<UserCredential>;
  signIn: (email: string, password: string) => Promise<UserCredential>;
  logout: () => Promise<void>;
  loading: boolean;
}

export const AuthContext = createContext<AuthContextType | null>(null);
const db = getFirestore();

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);

  // Fetch Firestore profile by email
  const fetchUserProfile = async (email: string) => {
    try {
      const userProfilesRef = collection(db, "user-profiles");
      const q = query(userProfilesRef, where("email", "==", email));
      const querySnapshot = await getDocs(q);

      if (!querySnapshot.empty) {
        const userDoc = querySnapshot.docs[0];
        setUserProfile(userDoc.data() as UserProfile);
      } else {
        console.log("No Firestore profile found for user with email:", email);
        setUserProfile(null);
      }
    } catch (error) {
      console.error("Error fetching user profile:", error);
      setUserProfile(null);
    }
  };

  async function signUp(email: string, password: string, phone: string) {
    const credential = await createUserWithEmailAndPassword(auth, email, password);

    // Create user profile in Firestore
    const userProfileData: UserProfile = {
      id: credential.user.uid,
      email: email,
      phone: phone,
      name: "", // Can be updated later in profile
      isSysAdmin: false, // Default value
      mosqueId: "", // Empty string by default
    };

    await setDoc(doc(db, "user-profiles", credential.user.uid), userProfileData);

    return credential;
  }

  async function signIn(email: string, password: string) {
    await setPersistence(auth, browserLocalPersistence);
    const credential = await signInWithEmailAndPassword(auth, email, password);
    console.log(credential);
    await fetchUserProfile(email);
    return credential;
  }

  async function logout() {
    setUserProfile(null);
    return signOut(auth);
  }

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      setCurrentUser(user);
      if (user) {
        await fetchUserProfile(user.email!);
      } else {
        setUserProfile(null);
      }
      setLoading(false);
    });

    return unsubscribe;
  }, []);

  const value = {
    currentUser,
    userProfile,
    signUp,
    signIn,
    logout,
    loading,
  };

  return <AuthContext.Provider value={value}>{!loading && children}</AuthContext.Provider>;
}
