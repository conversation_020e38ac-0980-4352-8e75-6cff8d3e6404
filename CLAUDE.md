# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Common Development Commands

- **Start development server**: `npm run dev`
- **Build for production**: `npm run build` (includes TypeScript compilation)
- **Lint code**: `npm run lint`
- **Preview production build**: `npm run preview`

## Environment Setup

1. Copy `.env.example` to `.env` and configure Firebase credentials:
```bash
cp .env.example .env
```
2. Fill in the Firebase configuration values in `.env` file with your project's credentials

## Project Architecture

This is a React + TypeScript mosque management application built with Vite, Firebase, and Tailwind CSS.

### Core Technologies
- **Frontend**: React 19 with TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS with dark mode support
- **Backend**: Firebase (Authentication, Firestore)
- **Routing**: React Router DOM v7
- **Date/Time**: hijri-date library for Islamic calendar support

### Architecture Overview

**Authentication System**:
- Firebase Authentication with local persistence
- Custom AuthContext (`src/contexts/AuthContext.tsx`) manages auth state
- User profiles stored in Firestore (`user-profiles` collection)
- ProtectedRoute component for route authorization

**State Management**:
- Context-based architecture with separate contexts for Auth and Theme
- AuthContext provides user authentication and profile data
- ThemeContext handles light/dark mode switching

**Data Models** (`src/models/`):
- `Mosque.ts` - Main mosque interface with comprehensive settings (prayer times, content, display settings)
- `User.ts` - User profile and authentication models
- `PrayerTime.ts` - Prayer time scheduling
- `MediaItem.ts` - Media/slider content

**Key Features**:
- Real-time version checking with automatic reload via Firestore listener
- Public mosque display pages (`/show/:friendlyUrlName`)
- Administrative dashboard for mosque management
- Prayer time calculations with timezone support
- Multilingual support (locale-based)
- Dark/light theme support

**Routing Structure**:
- `/` - Public homepage
- `/signin` - Authentication page
- `/show/:friendlyUrlName` - Public mosque display
- `/dashboard` - Protected admin dashboard
- `/edit-mosque` - Protected mosque editing
- `/profile` - Protected user profile

**Firebase Integration**:
- Authentication with email/password
- Firestore for mosque data and user profiles
- Real-time listeners for version control and data updates
- Environment-based configuration

**Development Notes**:
- Uses Cairo font family for Arabic/Islamic text display
- Tailwind configured with custom animations (blink effect)
- TypeScript strict mode enabled
- ESLint configured for code quality