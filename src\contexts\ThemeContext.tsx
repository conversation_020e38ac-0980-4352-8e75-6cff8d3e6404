import { useContext, useEffect, useState } from "react";
import { ThemeContext } from "./ThemeContext.context";

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const [isDarkMode, setIsDarkMode] = useState(() => {
    // Check if there's a saved theme preference in localStorage
    const saved = localStorage.getItem("darkMode");

    // If there's a saved value, use it; otherwise default to dark mode
    if (saved !== null) {
      try {
        const parsedValue = JSON.parse(saved);
        return parsedValue;
      } catch (error) {
        console.warn("Invalid theme preference in localStorage, defaulting to dark mode");
        return true;
      }
    }

    // No saved preference, default to dark mode
    return true;
  });

  useEffect(() => {
    // Apply the theme to the document
    if (isDarkMode) {
      document.documentElement.classList.add("dark");
    } else {
      document.documentElement.classList.remove("dark");
    }

    // Save the theme preference to localStorage
    localStorage.setItem("darkMode", JSON.stringify(isDarkMode));
  }, [isDarkMode]);

  const toggleDarkMode = () => {
    setIsDarkMode(!isDarkMode);
  };

  return <ThemeContext.Provider value={{ isDarkMode, toggleDarkMode }}>{children}</ThemeContext.Provider>;
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error("useTheme must be used within a ThemeProvider");
  }
  return context;
}
