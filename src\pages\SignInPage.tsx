/* eslint-disable @typescript-eslint/no-unused-vars */
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "../contexts/AuthContext";
import { FirebaseError } from "firebase/app";
import { FaMosque } from "react-icons/fa6";

export default function SignInPage() {
  const [isSignIn, setIsSignIn] = useState(true);
  const [phone, setPhone] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const { signIn, signUp } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setLoading(true);

    try {
      // Construct email from phone number
      const email = `p${phone}@almasajed.org`;

      if (isSignIn) {
        await signIn(email, password);
      } else {
        if (!phone) {
          // Validate phone for registration
          setError("رقم الهاتف مطلوب للتسجيل");
          setLoading(false);
          return;
        }
        try {
          await signUp(email, password, phone); // Pass phone to signUp
        } catch (error: unknown) {
          if (error instanceof FirebaseError) {
            if (error.code === "auth/email-already-in-use") {
              setError("هذا البريد الإلكتروني مسجل بالفعل. الرجاء تسجيل الدخول أو استخدام بريد إلكتروني آخر.");
              return;
            }
          }
          throw error;
        }
      }
      navigate("/dashboard");
    } catch (err) {
      setError("فشل في تسجيل الدخول. يرجى التحقق من بياناتك.");
      console.error(err);
    }

    setLoading(false);
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-white dark:bg-gray-900 transition-colors duration-200">
      <div className="bg-white dark:bg-gray-800 p-8 rounded-lg shadow-md w-96">
        {/* Add Mosque Icon */}
        <div className="flex justify-center mb-6">
          <FaMosque className="w-20 h-20 text-green-500" />
        </div>

        <h2 className="text-2xl font-bold mb-6 text-center text-gray-900 dark:text-white">{isSignIn ? "تسجيل الدخول" : "إنشاء حساب"}</h2>
        {error && <div className="bg-red-100 dark:bg-red-900 border border-red-400 text-red-700 dark:text-red-200 px-4 py-3 rounded mb-4">{error}</div>}
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block mb-1 text-gray-900 dark:text-white">رقم الهاتف</label>
            <input
              type="tel"
              value={phone}
              onChange={(e) => setPhone(e.target.value)}
              className="w-full p-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-right"
              required
            />
          </div>
          <div>
            <label className="block mb-1 text-gray-900 dark:text-white">كلمة المرور</label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full p-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              required
            />
          </div>
          <button type="submit" disabled={loading} className="w-full bg-green-500 hover:bg-green-600 text-white py-2 rounded-lg transition-colors disabled:bg-green-300">
            {loading ? "جاري التحميل..." : isSignIn ? "دخول" : "تسجيل"}
          </button>
        </form>
        <p className="mt-4 text-center text-gray-900 dark:text-white">
          {isSignIn ? "ليس لديك حساب؟" : "لديك حساب بالفعل؟"}
          <button onClick={() => setIsSignIn(!isSignIn)} className="text-green-500 hover:text-green-600 mr-1">
            {isSignIn ? "إنشاء حساب" : "تسجيل الدخول"}
          </button>
        </p>
      </div>
    </div>
  );
}
